# Code Locations and Structure
- Python/Cython source code: /Users/<USER>/Developer/nautilus/nautilus_trader/nautilus_trader
- Rust source code: /Users/<USER>/Developer/nautilus/nautilus_trader/crates
- Be careful when working on nautilus_trader to create files in /Users/<USER>/Developer/nautilus/nautilus_trader and NOT /Users/<USER>/Developer/nautilus
- User prefers importing crate functions at the beginning of files

# Development Workflow
- Always run the startup script when starting a terminal: eval "$(pyenv init --path)" && eval "$(pyenv virtualenv-init -)"
- Testing sequence: make cargo-test when modifying rust code, make build-debug, make pytest
- After modifying Rust or Cython code, run 'make build-debug' to make changes accessible in Python
- Use 'make clean' followed by 'make build-debug' to fix build/import issues
- Format Rust files with 'make format' after having modifed Rust files
- User prefers AI to perform testing directly rather than being instructed to test
- User prefers tests in proper place within codebase structure rather than standalone test files
- Run and fix precommit checks at the end of changes (in terminal run: pre-commit run)
- ONLY rebuild with 'make build-debug' after modifying Rust/Cython code, NOT Python code.
- User prefers to be asked for feedback after finishing tasks.
- To run individual files as script, use python and not uv run as the project is using a pyenv venv and not a uv venv.

# Code Style and Practices
- Add # noqa: C901 directive if a function exceeds complexity threshold, don't split it
- User prefers snake_case function naming over PascalCase message naming for consistency
- User prefers builder patterns for classes with many arguments
- When pyo3 Rust implementations conflict with existing Python/Cython names, use pyo3-specific naming
- User prefers debug outputs to be commented out rather than removed
- Use Option<T> in Rust and T with default values in Python bindings for optional parameters
- User prefers bool parameters with default values in Python bindings instead of Option<bool> in Rust
- User prefers to change tests to match existing function signatures rather than changing the function implementation when there are test failures.
- User prefers to keep method names singular and modify tests to match implementation rather than changing implementation to match tests.
- When using uv command directly use them with --active so no new uv virtualenv is created, for example `uv run --active`
- User prefers to keep .ts/.tsx file extensions in import statements rather than omitting them.
- User prefers using strategy config patterns similar to those in the notebooks folder when creating run_node.py files for examples.

# Trading and Order Execution
- Option combo execution requires matching engine integration for backtesting
- For option combo quote calculations, use instrument properties from one leg for quote parameters
- Use the greekscalculator component for pricing engines to obtain prices and greeks of option legs
- User prefers historical bars for option combo/spread contracts directly, not calculated from individual legs, and prefers not to implement workarounds if the broker API doesn't support it natively.

# Documentation and Presentation
- User prefers comprehensive documentation with broader design explanations
- User prefers using links to sections instead of just mentioning section names
- User prefers documentation to be clear, concise, and interesting with instructions written only once
- User prefers Quarto presentations with formulas split over multiple lines, bullet points followed by new lines
- Start the header levels in markdown at ## instead of # so I can copy PR summaries more easily in a PR template used in a project

# Data Processing
- In strategies, quote tick data should be processed in on_quote_tick method, not generic on_data method
- User prefers full orderbook updates over OrderBookDelta conversions for MBP-10 data

# Data Sources
- User prefers using Interactive Brokers over Databento for downloading ES options information.
- User prefers not using TWS gateway for IB connections.